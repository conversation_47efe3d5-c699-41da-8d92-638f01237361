# Gemini Project Analysis: gb28181-client-go

## Project Overview

This project is a GB/T 28181 protocol gateway service, named GB-Gateway, developed in Go. Its primary function is to serve as a bridge between a higher-level business platform and a lower-level national standard video platform. The gateway provides a simple RESTful HTTP API to abstract the complexities of the underlying GB/T 28181 SIP protocol.

**Key Features:**

*   **Protocol Conversion:** Translates simple HTTP RESTful API calls into complex GB/T 28181 SIP messages.
*   **Device Management:** Supports querying device lists and managing their status.
*   **Video Stream Control:** Handles video stream requests by sending SIP INVITE messages.
*   **PTZ Control:** Manages camera pan, tilt, and zoom operations via SIP MESSAGEs.
*   **Stateless Design:** Utilizes in-memory state management for quick deployment.
*   **Lightweight:** Focuses solely on signaling, without processing video streams, to ensure high performance.

**Architecture:**

The system is composed of several key components:

*   **Main Application (`cmd/gb-gateway/main.go`):** The entry point of the application, responsible for initializing and wiring together all the components.
*   **Configuration (`internal/config/config.go`):** Manages application settings using a `config.yaml` file.
*   **HTTP Server (`internal/http/server.go`):** Implemented with the Gin framework, it exposes the RESTful API for interacting with the gateway. It also serves the Swagger UI for API documentation.
*   **Core Logic (`internal/core/logic.go`):** Contains the main business logic, handling device management, stream requests, and PTZ control.
*   **SIP Server (`internal/sip/server.go`):** Built with the `go-av/gosip` library, this component handles all SIP communication with the video platform.
*   **State Manager (`internal/state/manager.go`):** An in-memory state manager for tracking devices, platforms, and sessions.

**Technology Stack:**

*   **Language:** Go
*   **Web Framework:** Gin
*   **SIP Protocol Stack:** go-av/gosip
*   **Configuration:** Viper
*   **Logging:** slog
*   **API Documentation:** Swagger/OpenAPI 3.0

## Building and Running

The project uses a `Makefile` to simplify the build and run process.

*   **Build the application:**
    ```bash
    make build
    ```
    This will create an executable named `gb-gateway` in the project root.

*   **Run the application:**
    ```bash
    make run
    ```
    This command first builds the project and then starts the server.

*   **Generate API documentation and run:**
    ```bash
    make docs
    ```
    This will generate the Swagger documentation and then start the server. The API documentation will be available at `http://localhost:8080/swagger/index.html` when the log level is set to "debug".

*   **Run tests:**
    ```bash
    make test
    ```

## Development Conventions

*   **Configuration:** The application is configured via a `config.yaml` file. An example configuration is provided in `config.example.yaml`.
*   **API Documentation:** API documentation is generated from code annotations using `swag`. The documentation is only enabled in "debug" mode for security reasons.
*   **Testing:** The project includes several shell scripts for testing different aspects of the application, such as `test.sh`, `test_ptz.sh`, and `test_swagger_security.sh`.
*   **Dependencies:** Project dependencies are managed using Go modules (`go.mod` and `go.sum`).
