package main

import (
	"fmt"
	"log/slog"
	"time"

	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"
)

func main() {
	// 创建状态管理器
	stateManager := state.NewInMemoryManager()

	// 模拟平台注册
	platform := &models.Platform{
		ID:       "34020000002000000001",
		SIPURI:   "sip:34020000002000000001@127.0.0.1:5060",
		Expires:  3600,
		LastSeen: time.Now(),
	}

	err := stateManager.RegisterPlatform(platform)
	if err != nil {
		slog.Error("Failed to register platform", "error", err)
		return
	}

	// 模拟设备数据
	devices := []*models.Device{
		{
			GBID:       "34020000001320000001",
			Name:       "前门摄像头test",
			Status:     "ON",
			IP:         "*************",
			PlatformID: platform.ID,
		},
		{
			GBID:       "34020000001320000002",
			Name:       "后门摄像头test",
			Status:     "ON",
			IP:         "*************",
			PlatformID: platform.ID,
		},
		{
			GBID:       "34020000001320000003",
			Name:       "大厅摄像头test",
			Status:     "OFF",
			IP:         "*************",
			PlatformID: platform.ID,
		},
	}

	err = stateManager.SetDevices(platform.ID, devices)
	if err != nil {
		slog.Error("Failed to set devices", "error", err)
		return
	}

	fmt.Println("Test data created successfully!")
	fmt.Printf("Platform ID: %s\n", platform.ID)
	fmt.Printf("Device count: %d\n", len(devices))

	// 验证数据
	retrievedDevices, err := stateManager.GetDevices(platform.ID)
	if err != nil {
		slog.Error("Failed to get devices", "error", err)
		return
	}

	fmt.Printf("Retrieved %d devices:\n", len(retrievedDevices))
	for _, device := range retrievedDevices {
		fmt.Printf("- %s: %s (%s)\n", device.GBID, device.Name, device.Status)
	}
}
